import { PDFDocument, PDFPage, rgb } from 'pdf-lib';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import config from '../config';
import logger from '../utils/logger';
import { FileUtils } from '../utils/fileUtils';
import { 
  PdfMergeOptions, 
  PdfSplitOptions, 
  PdfCompressOptions,
  ProcessedFile 
} from '../types';

export class PdfService {
  
  /**
   * Merge multiple PDF files into one
   */
  static async mergePdfs(options: PdfMergeOptions): Promise<ProcessedFile> {
    try {
      logger.info(`Starting PDF merge operation with ${options.files.length} files`);
      
      const mergedPdf = await PDFDocument.create();
      
      for (const file of options.files) {
        const pdfBytes = fs.readFileSync(file.path);
        const pdf = await PDFDocument.load(pdfBytes);
        const pages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
        
        pages.forEach((page) => mergedPdf.addPage(page));
        
        // Clean up temporary file
        await FileUtils.deleteFile(file.path);
      }
      
      // Generate output filename
      const outputName = options.outputName || `merged_${Date.now()}.pdf`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      // Save merged PDF
      const pdfBytes = await mergedPdf.save();
      fs.writeFileSync(outputPath, pdfBytes);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: pdfBytes.length,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info(`PDF merge completed successfully: ${outputName}`);
      return result;
      
    } catch (error) {
      logger.error('PDF merge error:', error);
      throw new Error('Failed to merge PDF files');
    }
  }
  
  /**
   * Split PDF file based on options
   */
  static async splitPdf(options: PdfSplitOptions): Promise<ProcessedFile[]> {
    try {
      logger.info(`Starting PDF split operation: ${options.splitType}`);
      
      const pdfBytes = fs.readFileSync(options.file.path);
      const pdf = await PDFDocument.load(pdfBytes);
      const totalPages = pdf.getPageCount();
      
      const results: ProcessedFile[] = [];
      
      if (options.splitType === 'all') {
        // Split each page into separate file
        for (let i = 0; i < totalPages; i++) {
          const newPdf = await PDFDocument.create();
          const [page] = await newPdf.copyPages(pdf, [i]);
          newPdf.addPage(page);
          
          const outputName = `page_${i + 1}_${Date.now()}.pdf`;
          const outputPath = path.join(config.paths.uploads, outputName);
          
          const pdfBytes = await newPdf.save();
          fs.writeFileSync(outputPath, pdfBytes);
          
          results.push({
            id: uuidv4(),
            originalName: outputName,
            filename: outputName,
            path: outputPath,
            size: pdfBytes.length,
            mimeType: 'application/pdf',
            createdAt: new Date()
          });
        }
      } else if (options.splitType === 'range' && options.pageRange) {
        // Split by page ranges
        const ranges = this.parsePageRanges(options.pageRange, totalPages);
        
        for (let i = 0; i < ranges.length; i++) {
          const range = ranges[i];
          const newPdf = await PDFDocument.create();
          const pages = await newPdf.copyPages(pdf, range);
          pages.forEach(page => newPdf.addPage(page));
          
          const outputName = `range_${range[0] + 1}-${range[range.length - 1] + 1}_${Date.now()}.pdf`;
          const outputPath = path.join(config.paths.uploads, outputName);
          
          const pdfBytes = await newPdf.save();
          fs.writeFileSync(outputPath, pdfBytes);
          
          results.push({
            id: uuidv4(),
            originalName: outputName,
            filename: outputName,
            path: outputPath,
            size: pdfBytes.length,
            mimeType: 'application/pdf',
            createdAt: new Date()
          });
        }
      } else if (options.splitType === 'specific' && options.specificPages) {
        // Split specific pages
        const pageNumbers = this.parseSpecificPages(options.specificPages, totalPages);
        
        for (const pageNum of pageNumbers) {
          const newPdf = await PDFDocument.create();
          const [page] = await newPdf.copyPages(pdf, [pageNum - 1]);
          newPdf.addPage(page);
          
          const outputName = `page_${pageNum}_${Date.now()}.pdf`;
          const outputPath = path.join(config.paths.uploads, outputName);
          
          const pdfBytes = await newPdf.save();
          fs.writeFileSync(outputPath, pdfBytes);
          
          results.push({
            id: uuidv4(),
            originalName: outputName,
            filename: outputName,
            path: outputPath,
            size: pdfBytes.length,
            mimeType: 'application/pdf',
            createdAt: new Date()
          });
        }
      }
      
      // Clean up original file
      await FileUtils.deleteFile(options.file.path);
      
      logger.info(`PDF split completed successfully: ${results.length} files created`);
      return results;
      
    } catch (error) {
      logger.error('PDF split error:', error);
      throw new Error('Failed to split PDF file');
    }
  }
  
  /**
   * Compress PDF file
   */
  static async compressPdf(options: PdfCompressOptions): Promise<ProcessedFile> {
    try {
      logger.info(`Starting PDF compression: ${options.compressionLevel}`);
      
      const pdfBytes = fs.readFileSync(options.file.path);
      const pdf = await PDFDocument.load(pdfBytes);
      
      // Apply compression based on level
      // Note: pdf-lib has limited compression options
      // For production, consider using other libraries like Ghostscript
      const compressedBytes = await pdf.save({
        useObjectStreams: options.compressionLevel !== 'low',
        addDefaultPage: false,
        objectsPerTick: options.compressionLevel === 'high' ? 50 : 20
      });
      
      const outputName = `compressed_${options.file.originalname}`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      fs.writeFileSync(outputPath, compressedBytes);
      
      // Clean up original file
      await FileUtils.deleteFile(options.file.path);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: compressedBytes.length,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info(`PDF compression completed: ${FileUtils.formatFileSize(options.file.size)} -> ${FileUtils.formatFileSize(compressedBytes.length)}`);
      return result;
      
    } catch (error) {
      logger.error('PDF compression error:', error);
      throw new Error('Failed to compress PDF file');
    }
  }
  
  /**
   * Rotate PDF pages
   */
  static async rotatePdf(file: Express.Multer.File, rotation: number, pages?: string): Promise<ProcessedFile> {
    try {
      logger.info(`Starting PDF rotation: ${rotation} degrees`);

      const pdfBytes = fs.readFileSync(file.path);
      const pdf = await PDFDocument.load(pdfBytes);
      const totalPages = pdf.getPageCount();

      // Determine which pages to rotate
      let pageIndices: number[];
      if (!pages || pages === 'all') {
        pageIndices = Array.from({ length: totalPages }, (_, i) => i);
      } else {
        pageIndices = this.parseSpecificPages(pages, totalPages).map(p => p - 1);
      }

      // Rotate specified pages
      for (const pageIndex of pageIndices) {
        const page = pdf.getPage(pageIndex);
        page.setRotation({ angle: rotation });
      }

      const outputName = `rotated_${file.originalname}`;
      const outputPath = path.join(config.paths.uploads, outputName);

      const rotatedBytes = await pdf.save();
      fs.writeFileSync(outputPath, rotatedBytes);

      // Clean up original file
      await FileUtils.deleteFile(file.path);

      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: rotatedBytes.length,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };

      logger.info(`PDF rotation completed successfully`);
      return result;

    } catch (error) {
      logger.error('PDF rotation error:', error);
      throw new Error('Failed to rotate PDF file');
    }
  }

  /**
   * Parse page ranges like "1-5, 8-12"
   */
  private static parsePageRanges(rangeStr: string, totalPages: number): number[][] {
    const ranges: number[][] = [];
    const parts = rangeStr.split(',').map(s => s.trim());

    for (const part of parts) {
      if (part.includes('-')) {
        const [start, end] = part.split('-').map(s => parseInt(s.trim()));
        const range: number[] = [];
        for (let i = Math.max(1, start); i <= Math.min(totalPages, end); i++) {
          range.push(i - 1); // Convert to 0-based index
        }
        if (range.length > 0) ranges.push(range);
      } else {
        const pageNum = parseInt(part);
        if (pageNum >= 1 && pageNum <= totalPages) {
          ranges.push([pageNum - 1]); // Convert to 0-based index
        }
      }
    }

    return ranges;
  }

  /**
   * Parse specific pages like "1, 3, 5, 7"
   */
  private static parseSpecificPages(pagesStr: string, totalPages: number): number[] {
    const pages: number[] = [];
    const parts = pagesStr.split(',').map(s => s.trim());

    for (const part of parts) {
      const pageNum = parseInt(part);
      if (pageNum >= 1 && pageNum <= totalPages) {
        pages.push(pageNum);
      }
    }

    return pages;
  }
}
