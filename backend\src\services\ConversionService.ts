import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import { PDFDocument, rgb, StandardFonts } from 'pdf-lib';
import config from '../config';
import logger from '../utils/logger';
import { FileUtils } from '../utils/fileUtils';
import { PdfConversionOptions, ProcessedFile } from '../types';

export class ConversionService {
  
  /**
   * Convert PDF to images (JPG/PNG)
   */
  static async pdfToImage(options: PdfConversionOptions): Promise<ProcessedFile[]> {
    try {
      logger.info(`Starting PDF to image conversion: ${options.outputFormat}`);
      
      // For now, we'll use a placeholder implementation
      // In production, you'd use pdf2pic or similar library
      const results: ProcessedFile[] = [];
      
      // Simulate conversion by creating placeholder images
      const pdfBytes = fs.readFileSync(options.file.path);
      const pdf = await PDFDocument.load(pdfBytes);
      const pageCount = pdf.getPageCount();
      
      for (let i = 0; i < pageCount; i++) {
        // Create a placeholder image using sharp
        const width = 595; // A4 width in points
        const height = 842; // A4 height in points
        
        const imageBuffer = await sharp({
          create: {
            width,
            height,
            channels: 3,
            background: { r: 255, g: 255, b: 255 }
          }
        })
        .jpeg({ quality: options.quality === 'high' ? 95 : options.quality === 'medium' ? 75 : 50 })
        .toBuffer();
        
        const outputName = `page_${i + 1}_${Date.now()}.${options.outputFormat}`;
        const outputPath = path.join(config.paths.uploads, outputName);
        
        fs.writeFileSync(outputPath, imageBuffer);
        
        results.push({
          id: uuidv4(),
          originalName: outputName,
          filename: outputName,
          path: outputPath,
          size: imageBuffer.length,
          mimeType: `image/${options.outputFormat}`,
          createdAt: new Date()
        });
      }
      
      // Clean up original file
      await FileUtils.deleteFile(options.file.path);
      
      logger.info(`PDF to image conversion completed: ${results.length} images created`);
      return results;
      
    } catch (error) {
      logger.error('PDF to image conversion error:', error);
      throw new Error('Failed to convert PDF to images');
    }
  }
  
  /**
   * Convert PDF to Word document
   */
  static async pdfToWord(file: Express.Multer.File): Promise<ProcessedFile> {
    try {
      logger.info('Starting PDF to Word conversion');
      
      // This is a placeholder implementation
      // In production, you'd use a proper PDF to Word conversion library
      const outputName = `converted_${path.basename(file.originalname, '.pdf')}.docx`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      // Create a simple Word document with placeholder content
      const docContent = `
        <html>
          <body>
            <h1>Converted from PDF</h1>
            <p>This is a placeholder conversion from ${file.originalname}</p>
            <p>In a production environment, this would contain the actual extracted text and formatting from the PDF.</p>
          </body>
        </html>
      `;
      
      // Convert HTML to Word using mammoth (reverse process)
      // Note: This is a simplified approach for demonstration
      fs.writeFileSync(outputPath, docContent);
      
      // Clean up original file
      await FileUtils.deleteFile(file.path);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: Buffer.byteLength(docContent),
        mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        createdAt: new Date()
      };
      
      logger.info('PDF to Word conversion completed');
      return result;
      
    } catch (error) {
      logger.error('PDF to Word conversion error:', error);
      throw new Error('Failed to convert PDF to Word');
    }
  }
  
  /**
   * Convert Word document to PDF
   */
  static async wordToPdf(file: Express.Multer.File): Promise<ProcessedFile> {
    try {
      logger.info('Starting Word to PDF conversion');
      
      // Extract text from Word document
      const result = await mammoth.extractRawText({ path: file.path });
      const text = result.value;
      
      // Create PDF from extracted text
      const pdfDoc = await PDFDocument.create();
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
      
      // Split text into lines and pages
      const lines = text.split('\n');
      const linesPerPage = 40;
      const pageWidth = 595;
      const pageHeight = 842;
      const margin = 50;
      
      for (let i = 0; i < lines.length; i += linesPerPage) {
        const page = pdfDoc.addPage([pageWidth, pageHeight]);
        const pageLines = lines.slice(i, i + linesPerPage);
        
        pageLines.forEach((line, index) => {
          page.drawText(line, {
            x: margin,
            y: pageHeight - margin - (index * 20),
            size: 12,
            font,
            color: rgb(0, 0, 0),
          });
        });
      }
      
      const outputName = `converted_${path.basename(file.originalname, path.extname(file.originalname))}.pdf`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      const pdfBytes = await pdfDoc.save();
      fs.writeFileSync(outputPath, pdfBytes);
      
      // Clean up original file
      await FileUtils.deleteFile(file.path);
      
      const processedFile: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: pdfBytes.length,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info('Word to PDF conversion completed');
      return processedFile;
      
    } catch (error) {
      logger.error('Word to PDF conversion error:', error);
      throw new Error('Failed to convert Word to PDF');
    }
  }
  
  /**
   * Convert Excel to PDF
   */
  static async excelToPdf(file: Express.Multer.File): Promise<ProcessedFile> {
    try {
      logger.info('Starting Excel to PDF conversion');
      
      // Read Excel file
      const workbook = XLSX.readFile(file.path);
      const sheetNames = workbook.SheetNames;
      
      // Create PDF
      const pdfDoc = await PDFDocument.create();
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
      
      for (const sheetName of sheetNames) {
        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        
        const page = pdfDoc.addPage([842, 595]); // Landscape for better table display
        
        // Draw sheet name as title
        page.drawText(sheetName, {
          x: 50,
          y: 545,
          size: 16,
          font,
          color: rgb(0, 0, 0),
        });
        
        // Draw table data
        let yPosition = 500;
        for (const row of jsonData as any[][]) {
          if (yPosition < 50) break; // Avoid going off page
          
          let xPosition = 50;
          for (const cell of row) {
            if (xPosition > 750) break; // Avoid going off page
            
            page.drawText(String(cell || ''), {
              x: xPosition,
              y: yPosition,
              size: 10,
              font,
              color: rgb(0, 0, 0),
            });
            
            xPosition += 100; // Column width
          }
          yPosition -= 20; // Row height
        }
      }
      
      const outputName = `converted_${path.basename(file.originalname, path.extname(file.originalname))}.pdf`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      const pdfBytes = await pdfDoc.save();
      fs.writeFileSync(outputPath, pdfBytes);
      
      // Clean up original file
      await FileUtils.deleteFile(file.path);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: pdfBytes.length,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info('Excel to PDF conversion completed');
      return result;
      
    } catch (error) {
      logger.error('Excel to PDF conversion error:', error);
      throw new Error('Failed to convert Excel to PDF');
    }
  }
  
  /**
   * Convert images to PDF
   */
  static async imagesToPdf(files: Express.Multer.File[]): Promise<ProcessedFile> {
    try {
      logger.info(`Starting images to PDF conversion: ${files.length} images`);
      
      const pdfDoc = await PDFDocument.create();
      
      for (const file of files) {
        const imageBytes = fs.readFileSync(file.path);
        
        let image;
        if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/jpg') {
          image = await pdfDoc.embedJpg(imageBytes);
        } else if (file.mimetype === 'image/png') {
          image = await pdfDoc.embedPng(imageBytes);
        } else {
          // Convert other formats to JPEG using sharp
          const jpegBytes = await sharp(imageBytes).jpeg().toBuffer();
          image = await pdfDoc.embedJpg(jpegBytes);
        }
        
        const page = pdfDoc.addPage();
        const { width, height } = page.getSize();
        
        // Scale image to fit page while maintaining aspect ratio
        const imageAspectRatio = image.width / image.height;
        const pageAspectRatio = width / height;
        
        let imageWidth, imageHeight;
        if (imageAspectRatio > pageAspectRatio) {
          imageWidth = width - 100; // 50px margin on each side
          imageHeight = imageWidth / imageAspectRatio;
        } else {
          imageHeight = height - 100; // 50px margin on top and bottom
          imageWidth = imageHeight * imageAspectRatio;
        }
        
        page.drawImage(image, {
          x: (width - imageWidth) / 2,
          y: (height - imageHeight) / 2,
          width: imageWidth,
          height: imageHeight,
        });
        
        // Clean up temporary file
        await FileUtils.deleteFile(file.path);
      }
      
      const outputName = `images_to_pdf_${Date.now()}.pdf`;
      const outputPath = path.join(config.paths.uploads, outputName);
      
      const pdfBytes = await pdfDoc.save();
      fs.writeFileSync(outputPath, pdfBytes);
      
      const result: ProcessedFile = {
        id: uuidv4(),
        originalName: outputName,
        filename: outputName,
        path: outputPath,
        size: pdfBytes.length,
        mimeType: 'application/pdf',
        createdAt: new Date()
      };
      
      logger.info('Images to PDF conversion completed');
      return result;
      
    } catch (error) {
      logger.error('Images to PDF conversion error:', error);
      throw new Error('Failed to convert images to PDF');
    }
  }
}
