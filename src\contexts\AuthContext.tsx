import React, { createContext, useContext, useState, useEffect } from 'react';

export interface User {
  id: string;
  email: string;
  name: string;
  subscription: 'free' | 'premium';
  subscriptionExpiry?: Date;
  usage: {
    filesProcessed: number;
    monthlyLimit: number;
    resetDate: Date;
  };
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  logout: () => void;
  updateSubscription: (subscription: 'free' | 'premium') => void;
  checkUsageLimit: () => boolean;
  incrementUsage: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for existing session on app load
    const savedUser = localStorage.getItem('user');
    const savedToken = localStorage.getItem('authToken');
    
    if (savedUser && savedToken) {
      try {
        const userData = JSON.parse(savedUser);
        // Convert date strings back to Date objects
        if (userData.subscriptionExpiry) {
          userData.subscriptionExpiry = new Date(userData.subscriptionExpiry);
        }
        if (userData.usage?.resetDate) {
          userData.usage.resetDate = new Date(userData.usage.resetDate);
        }
        setUser(userData);
      } catch (error) {
        console.error('Error parsing saved user data:', error);
        localStorage.removeItem('user');
        localStorage.removeItem('authToken');
      }
    }
    setIsLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock user data - replace with actual API response
      const userData: User = {
        id: '1',
        email,
        name: email.split('@')[0],
        subscription: 'free',
        usage: {
          filesProcessed: 0,
          monthlyLimit: 10,
          resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
        }
      };
      
      const token = 'mock-jwt-token'; // Replace with actual JWT token
      
      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));
      localStorage.setItem('authToken', token);
    } catch (error) {
      throw new Error('Invalid credentials');
    } finally {
      setIsLoading(false);
    }
  };

  const register = async (email: string, password: string, name: string): Promise<void> => {
    setIsLoading(true);
    try {
      // Simulate API call - replace with actual API integration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock user creation - replace with actual API response
      const userData: User = {
        id: Date.now().toString(),
        email,
        name,
        subscription: 'free',
        usage: {
          filesProcessed: 0,
          monthlyLimit: 10,
          resetDate: new Date(new Date().getFullYear(), new Date().getMonth() + 1, 1)
        }
      };
      
      const token = 'mock-jwt-token'; // Replace with actual JWT token
      
      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));
      localStorage.setItem('authToken', token);
    } catch (error) {
      throw new Error('Registration failed');
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
    localStorage.removeItem('authToken');
  };

  const updateSubscription = (subscription: 'free' | 'premium') => {
    if (!user) return;
    
    const updatedUser: User = {
      ...user,
      subscription,
      subscriptionExpiry: subscription === 'premium' 
        ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
        : undefined,
      usage: {
        ...user.usage,
        monthlyLimit: subscription === 'premium' ? 1000 : 10
      }
    };
    
    setUser(updatedUser);
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };

  const checkUsageLimit = (): boolean => {
    if (!user) return false;
    
    // Check if usage period has reset
    const now = new Date();
    if (now >= user.usage.resetDate) {
      // Reset usage for new month
      const updatedUser: User = {
        ...user,
        usage: {
          ...user.usage,
          filesProcessed: 0,
          resetDate: new Date(now.getFullYear(), now.getMonth() + 1, 1)
        }
      };
      setUser(updatedUser);
      localStorage.setItem('user', JSON.stringify(updatedUser));
      return true;
    }
    
    return user.usage.filesProcessed < user.usage.monthlyLimit;
  };

  const incrementUsage = () => {
    if (!user) return;
    
    const updatedUser: User = {
      ...user,
      usage: {
        ...user.usage,
        filesProcessed: user.usage.filesProcessed + 1
      }
    };
    
    setUser(updatedUser);
    localStorage.setItem('user', JSON.stringify(updatedUser));
  };

  const value: AuthContextType = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    updateSubscription,
    checkUsageLimit,
    incrementUsage
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
