import React from 'react';
import { Crown, AlertTriangle } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

interface UsageLimitGateProps {
  children: React.ReactNode;
  onUpgrade?: () => void;
}

const UsageLimitGate: React.FC<UsageLimitGateProps> = ({ children, onUpgrade }) => {
  const { user, checkUsageLimit, updateSubscription } = useAuth();

  if (!user) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 text-center">
        <AlertTriangle className="w-12 h-12 text-yellow-600 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-yellow-800 mb-2">
          Connexion requise
        </h3>
        <p className="text-yellow-700 mb-4">
          Veuillez vous connecter pour utiliser les outils PDF.
        </p>
        <button
          onClick={() => window.location.href = '/login'}
          className="bg-yellow-600 text-white px-6 py-2 rounded-lg hover:bg-yellow-700 transition-colors"
        >
          Se connecter
        </button>
      </div>
    );
  }

  const canProcess = checkUsageLimit();
  const usagePercentage = (user.usage.filesProcessed / user.usage.monthlyLimit) * 100;
  const isNearLimit = usagePercentage > 80;

  if (!canProcess) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
        <AlertTriangle className="w-12 h-12 text-red-600 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-red-800 mb-2">
          Limite mensuelle atteinte
        </h3>
        <p className="text-red-700 mb-4">
          Vous avez utilisé tous vos {user.usage.monthlyLimit} fichiers pour ce mois.
        </p>
        <div className="space-y-3">
          <button
            onClick={() => {
              updateSubscription('premium');
              onUpgrade?.();
            }}
            className="bg-gradient-to-r from-yellow-600 to-orange-600 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all duration-200 transform hover:scale-105 flex items-center justify-center space-x-2 mx-auto"
          >
            <Crown className="w-5 h-5" />
            <span>Passer Premium - 1000 fichiers/mois</span>
          </button>
          <p className="text-sm text-gray-600">
            Ou attendez le {user.usage.resetDate.toLocaleDateString('fr-FR')} pour la réinitialisation
          </p>
        </div>
      </div>
    );
  }

  if (isNearLimit && user.subscription === 'free') {
    return (
      <div className="space-y-4">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mr-2" />
            <div className="flex-1">
              <p className="text-yellow-800 font-medium">
                Attention : {user.usage.filesProcessed}/{user.usage.monthlyLimit} fichiers utilisés
              </p>
              <p className="text-yellow-700 text-sm">
                Plus que {user.usage.monthlyLimit - user.usage.filesProcessed} fichiers restants ce mois.
              </p>
            </div>
            <button
              onClick={() => {
                updateSubscription('premium');
                onUpgrade?.();
              }}
              className="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors text-sm flex items-center space-x-1"
            >
              <Crown className="w-4 h-4" />
              <span>Premium</span>
            </button>
          </div>
        </div>
        {children}
      </div>
    );
  }

  return <>{children}</>;
};

export default UsageLimitGate;
