<!DOCTYPE html>
<html>
<head>
    <title>Test API Connection</title>
</head>
<body>
    <h1>Test API Connection</h1>
    <button onclick="testHealth()">Test Health Endpoint</button>
    <div id="result"></div>

    <script>
        async function testHealth() {
            try {
                console.log('Testing health endpoint...');
                const response = await fetch('http://localhost:3001/api/health');
                console.log('Response:', response);
                const data = await response.json();
                console.log('Data:', data);
                document.getElementById('result').innerHTML = '<h3>Success:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = '<h3>Error:</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
