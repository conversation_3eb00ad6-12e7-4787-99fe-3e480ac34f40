import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import logger from '../utils/logger';

export const validateRequest = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error } = schema.validate(req.body);
    
    if (error) {
      logger.warn('Validation error:', error.details[0].message);
      return res.status(400).json({
        success: false,
        error: error.details[0].message
      });
    }
    
    next();
  };
};

// Validation schemas
export const schemas = {
  pdfSplit: Joi.object({
    splitType: Joi.string().valid('all', 'range', 'specific').required(),
    pageRange: Joi.when('splitType', {
      is: 'range',
      then: Joi.string().pattern(/^(\d+(-\d+)?,?\s*)+$/).required(),
      otherwise: Joi.optional()
    }),
    specificPages: Joi.when('splitType', {
      is: 'specific',
      then: Joi.string().pattern(/^(\d+,?\s*)+$/).required(),
      otherwise: Joi.optional()
    })
  }),

  pdfCompress: Joi.object({
    compressionLevel: Joi.string().valid('low', 'medium', 'high').required()
  }),

  pdfConvert: Joi.object({
    outputFormat: Joi.string().required(),
    quality: Joi.string().valid('low', 'medium', 'high').optional()
  }),

  pdfProtect: Joi.object({
    password: Joi.string().min(4).max(50).required(),
    permissions: Joi.object({
      printing: Joi.boolean().optional(),
      modifying: Joi.boolean().optional(),
      copying: Joi.boolean().optional(),
      annotating: Joi.boolean().optional()
    }).optional()
  }),

  pdfWatermark: Joi.object({
    watermarkText: Joi.string().max(100).optional(),
    position: Joi.string().valid('center', 'top-left', 'top-right', 'bottom-left', 'bottom-right').required(),
    opacity: Joi.number().min(0.1).max(1.0).required()
  }),

  ocr: Joi.object({
    language: Joi.string().valid('fr', 'en', 'es', 'de', 'auto').required()
  }),

  pageNumbers: Joi.object({
    position: Joi.string().valid('top-left', 'top-center', 'top-right', 'bottom-left', 'bottom-center', 'bottom-right').required(),
    startNumber: Joi.number().min(1).optional(),
    format: Joi.string().valid('number', 'roman', 'letter').optional()
  }),

  pdfRotate: Joi.object({
    rotation: Joi.number().valid(90, 180, 270).required(),
    pages: Joi.string().optional() // "all" or "1,3,5" or "1-5"
  })
};

// File validation middleware
export const validateFiles = (req: Request, res: Response, next: NextFunction) => {
  const files = req.files as Express.Multer.File[] | undefined;
  const file = req.file as Express.Multer.File | undefined;
  
  if (!files && !file) {
    return res.status(400).json({
      success: false,
      error: 'No files uploaded'
    });
  }
  
  // Check if we have files for operations that require them
  const endpoint = req.path;
  
  if (endpoint.includes('/merge') && (!files || files.length < 2)) {
    return res.status(400).json({
      success: false,
      error: 'At least 2 files are required for merging'
    });
  }
  
  if (endpoint.includes('/compare') && (!files || files.length !== 2)) {
    return res.status(400).json({
      success: false,
      error: 'Exactly 2 files are required for comparison'
    });
  }
  
  next();
};
