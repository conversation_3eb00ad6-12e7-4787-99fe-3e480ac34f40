import React from 'react';
import { useNavigate } from 'react-router-dom';
import RegisterForm from '../components/auth/RegisterForm';

const Register: React.FC = () => {
  const navigate = useNavigate();

  const handleRegisterSuccess = () => {
    // Redirect to dashboard after successful registration
    navigate('/dashboard', { replace: true });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center py-12 px-4">
      <RegisterForm onSuccess={handleRegisterSuccess} />
    </div>
  );
};

export default Register;
