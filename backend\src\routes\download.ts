import { Router, Request, Response } from 'express';
import path from 'path';
import fs from 'fs';
import config from '../config';
import logger from '../utils/logger';

const router = Router();

router.get('/:fileId', async (req: Request, res: Response) => {
  try {
    const { fileId } = req.params;
    
    // Security: validate fileId format (should be a UUID or safe filename)
    if (!/^[a-zA-Z0-9_-]+\.[a-zA-Z0-9]+$/.test(fileId)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid file ID format'
      });
    }
    
    const filePath = path.join(config.paths.uploads, fileId);
    
    // Check if file exists
    if (!fs.existsSync(filePath)) {
      logger.warn(`Download attempt for non-existent file: ${fileId}`);
      return res.status(404).json({
        success: false,
        error: 'File not found'
      });
    }
    
    // Get file stats
    const stats = fs.statSync(filePath);
    const fileSize = stats.size;
    
    // Set appropriate headers
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Length', fileSize);
    res.setHeader('Content-Disposition', `attachment; filename="${fileId}"`);
    res.setHeader('Cache-Control', 'no-cache');
    
    // Stream the file
    const fileStream = fs.createReadStream(filePath);
    
    fileStream.on('error', (error) => {
      logger.error(`Error streaming file ${fileId}:`, error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          error: 'Error downloading file'
        });
      }
    });
    
    fileStream.on('end', () => {
      logger.info(`File downloaded successfully: ${fileId}`);
    });
    
    fileStream.pipe(res);
    
  } catch (error) {
    logger.error('Download error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
