import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import Navbar from './components/Navbar';
import ProtectedRoute from './components/auth/ProtectedRoute';
import Login from './pages/Login';
import Register from './pages/Register';
import Dashboard from './pages/Dashboard';
import LandingPage from './pages/LandingPage';
import ToolsPage from './pages/ToolsPage';
import MergePDF from './pages/MergePDF';
import SplitPDF from './pages/SplitPDF';
import CompressPDF from './pages/CompressPDF';
import PDFToWord from './pages/PDFToWord';
import PDFToPowerPoint from './pages/PDFToPowerPoint';
import PDFToExcel from './pages/PDFToExcel';
import WordToPDF from './pages/WordToPDF';
import PowerPointToPDF from './pages/PowerPointToPDF';
import ExcelToPDF from './pages/ExcelToPDF';
import EditPDF from './pages/EditPDF';
import PDFToJPG from './pages/PDFToJPG';
import JPGToPDF from './pages/JPGToPDF';
import SignPDF from './pages/SignPDF';
import WatermarkPDF from './pages/WatermarkPDF';
import RotatePDF from './pages/RotatePDF';
import HTMLToPDF from './pages/HTMLToPDF';
import UnlockPDF from './pages/UnlockPDF';
import ProtectPDF from './pages/ProtectPDF';
import OrganizePDF from './pages/OrganizePDF';
import PDFToPDFA from './pages/PDFToPDFA';
import RepairPDF from './pages/RepairPDF';
import PageNumbers from './pages/PageNumbers';
import ScanToPDF from './pages/ScanToPDF';
import OCRPDF from './pages/OCRPDF';
import ComparePDF from './pages/ComparePDF';
import RedactPDF from './pages/RedactPDF';
import CropPDF from './pages/CropPDF';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Navbar />
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/tools" element={<ToolsPage />} />
          <Route path="/merge-pdf" element={<MergePDF />} />
          <Route path="/split-pdf" element={<SplitPDF />} />
          <Route path="/compress-pdf" element={<CompressPDF />} />
          <Route path="/pdf-to-word" element={<PDFToWord />} />
          <Route path="/pdf-to-powerpoint" element={<PDFToPowerPoint />} />
          <Route path="/pdf-to-excel" element={<PDFToExcel />} />
          <Route path="/word-to-pdf" element={<WordToPDF />} />
          <Route path="/powerpoint-to-pdf" element={<PowerPointToPDF />} />
          <Route path="/excel-to-pdf" element={<ExcelToPDF />} />
          <Route path="/edit-pdf" element={<EditPDF />} />
          <Route path="/pdf-to-jpg" element={<PDFToJPG />} />
          <Route path="/jpg-to-pdf" element={<JPGToPDF />} />
          <Route path="/sign-pdf" element={<SignPDF />} />
          <Route path="/watermark-pdf" element={<WatermarkPDF />} />
          <Route path="/rotate-pdf" element={<RotatePDF />} />
          <Route path="/html-to-pdf" element={<HTMLToPDF />} />
          <Route path="/unlock-pdf" element={<UnlockPDF />} />
          <Route path="/protect-pdf" element={<ProtectPDF />} />
          <Route path="/organize-pdf" element={<OrganizePDF />} />
          <Route path="/pdf-to-pdfa" element={<PDFToPDFA />} />
          <Route path="/repair-pdf" element={<RepairPDF />} />
          <Route path="/page-numbers" element={<PageNumbers />} />
          <Route path="/scan-to-pdf" element={<ScanToPDF />} />
          <Route path="/ocr-pdf" element={<OCRPDF />} />
          <Route path="/compare-pdf" element={<ComparePDF />} />
          <Route path="/redact-pdf" element={<RedactPDF />} />
          <Route path="/crop-pdf" element={<CropPDF />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;