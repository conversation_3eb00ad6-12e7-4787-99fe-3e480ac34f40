const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

// Create a simple test PDF using pdf-lib
const { PDFDocument, StandardFonts, rgb } = require('pdf-lib');

async function createTestPdf(filename) {
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([595, 842]);
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  
  page.drawText(`Test PDF: ${filename}`, {
    x: 50,
    y: 750,
    size: 20,
    font,
    color: rgb(0, 0, 0),
  });
  
  const pdfBytes = await pdfDoc.save();
  const filePath = path.join(__dirname, filename);
  fs.writeFileSync(filePath, pdfBytes);
  return filePath;
}

async function testMerge() {
  try {
    console.log('Creating test PDFs...');
    const pdf1Path = await createTestPdf('test1.pdf');
    const pdf2Path = await createTestPdf('test2.pdf');
    
    console.log('Creating form data...');
    const formData = new FormData();
    formData.append('files', fs.createReadStream(pdf1Path));
    formData.append('files', fs.createReadStream(pdf2Path));
    
    console.log('Sending request to merge endpoint...');
    const fetch = (await import('node-fetch')).default;
    const response = await fetch('http://localhost:3001/api/pdf/merge', {
      method: 'POST',
      body: formData,
    });
    
    const result = await response.json();
    console.log('Response status:', response.status);
    console.log('Response body:', JSON.stringify(result, null, 2));
    
    if (result.success && result.data && result.data.downloadUrl) {
      console.log('✅ Merge successful!');
      console.log('Download URL:', result.data.downloadUrl);
      
      // Test download
      console.log('Testing download...');
      const downloadResponse = await fetch(`http://localhost:3001${result.data.downloadUrl}`);
      console.log('Download status:', downloadResponse.status);
      
      if (downloadResponse.ok) {
        console.log('✅ Download successful!');
      } else {
        console.log('❌ Download failed');
      }
    } else {
      console.log('❌ Merge failed');
    }
    
    // Clean up test files
    fs.unlinkSync(pdf1Path);
    fs.unlinkSync(pdf2Path);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

testMerge();
