const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ProcessedFile {
  fileId: string;
  originalName: string;
  size: number;
  downloadUrl: string;
}

export interface ConversionResult {
  fileId?: string;
  files?: ProcessedFile[];
  originalName?: string;
  size?: number;
  downloadUrl?: string;
  compressionRatio?: string;
  originalSize?: number;
  compressedSize?: number;
}

class ApiService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          ...options.headers,
        },
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  private createFormData(files: File[], additionalData?: Record<string, any>): FormData {
    const formData = new FormData();
    
    if (files.length === 1) {
      formData.append('file', files[0]);
    } else {
      files.forEach(file => {
        formData.append('files', file);
      });
    }

    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, typeof value === 'object' ? JSON.stringify(value) : value);
      });
    }

    return formData;
  }

  // PDF Core Operations
  async mergePdfs(files: File[], outputName?: string): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData(files, { outputName });
    
    return this.makeRequest<ConversionResult>('/pdf/merge', {
      method: 'POST',
      body: formData,
    });
  }

  async splitPdf(
    file: File,
    splitType: 'all' | 'range' | 'specific',
    pageRange?: string,
    specificPages?: string
  ): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file], {
      splitType,
      pageRange,
      specificPages,
    });

    return this.makeRequest<ConversionResult>('/pdf/split', {
      method: 'POST',
      body: formData,
    });
  }

  async compressPdf(
    file: File,
    compressionLevel: 'low' | 'medium' | 'high'
  ): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file], { compressionLevel });

    return this.makeRequest<ConversionResult>('/pdf/compress', {
      method: 'POST',
      body: formData,
    });
  }

  async rotatePdf(
    file: File,
    rotation: number,
    pages?: string
  ): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file], { rotation, pages });

    return this.makeRequest<ConversionResult>('/pdf/rotate', {
      method: 'POST',
      body: formData,
    });
  }

  // PDF Conversion Operations
  async pdfToImages(
    file: File,
    outputFormat: string = 'jpg',
    quality: 'low' | 'medium' | 'high' = 'medium'
  ): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file], { outputFormat, quality });

    return this.makeRequest<ConversionResult>('/pdf/convert/to-images', {
      method: 'POST',
      body: formData,
    });
  }

  async pdfToWord(file: File): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file]);

    return this.makeRequest<ConversionResult>('/pdf/convert/to-word', {
      method: 'POST',
      body: formData,
    });
  }

  async pdfToExcel(file: File): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file]);

    return this.makeRequest<ConversionResult>('/pdf/convert/to-excel', {
      method: 'POST',
      body: formData,
    });
  }

  async pdfToPowerPoint(file: File): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file]);

    return this.makeRequest<ConversionResult>('/pdf/convert/to-powerpoint', {
      method: 'POST',
      body: formData,
    });
  }

  // Other formats to PDF
  async wordToPdf(file: File): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file]);

    return this.makeRequest<ConversionResult>('/pdf/convert/from-word', {
      method: 'POST',
      body: formData,
    });
  }

  async excelToPdf(file: File): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file]);

    return this.makeRequest<ConversionResult>('/pdf/convert/from-excel', {
      method: 'POST',
      body: formData,
    });
  }

  async powerPointToPdf(file: File): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData([file]);

    return this.makeRequest<ConversionResult>('/pdf/convert/from-powerpoint', {
      method: 'POST',
      body: formData,
    });
  }

  async imagesToPdf(files: File[]): Promise<ApiResponse<ConversionResult>> {
    const formData = this.createFormData(files);

    return this.makeRequest<ConversionResult>('/pdf/convert/from-images', {
      method: 'POST',
      body: formData,
    });
  }

  // Download file
  async downloadFile(fileId: string): Promise<Blob> {
    const response = await fetch(`${API_BASE_URL}/download/${fileId}`);
    
    if (!response.ok) {
      throw new Error(`Download failed: ${response.statusText}`);
    }
    
    return response.blob();
  }

  // Health check
  async healthCheck(): Promise<ApiResponse> {
    return this.makeRequest('/health');
  }
}

export const apiService = new ApiService();
export default apiService;
