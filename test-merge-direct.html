<!DOCTYPE html>
<html>
<head>
    <title>Test Merge Direct</title>
</head>
<body>
    <h1>Test PDF Merge Direct</h1>
    <input type="file" id="fileInput" multiple accept=".pdf">
    <button onclick="testMerge()">Test Merge</button>
    <div id="result"></div>

    <script>
        async function testMerge() {
            const fileInput = document.getElementById('fileInput');
            const files = fileInput.files;
            
            if (files.length < 2) {
                alert('Please select at least 2 PDF files');
                return;
            }

            try {
                console.log('Testing merge with files:', Array.from(files).map(f => f.name));
                
                const formData = new FormData();
                Array.from(files).forEach(file => {
                    formData.append('files', file);
                });

                console.log('Sending request to merge endpoint...');
                const response = await fetch('http://localhost:3001/api/pdf/merge', {
                    method: 'POST',
                    body: formData
                });

                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok && data.success) {
                    document.getElementById('result').innerHTML = 
                        '<h3>Success:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>' +
                        '<br><a href="http://localhost:3001' + data.data.downloadUrl + '" target="_blank">Download Merged PDF</a>';
                } else {
                    document.getElementById('result').innerHTML = '<h3>Error:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                }
            } catch (error) {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = '<h3>Error:</h3><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
