import React from 'react';
import { User, Crown, FileText, Calendar, Settings, LogOut, CreditCard } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const UserDashboard: React.FC = () => {
  const { user, logout, updateSubscription } = useAuth();

  if (!user) return null;

  const usagePercentage = (user.usage.filesProcessed / user.usage.monthlyLimit) * 100;
  const isNearLimit = usagePercentage > 80;
  const isPremium = user.subscription === 'premium';

  const handleUpgradeToPremium = () => {
    // In production, this would integrate with Stripe/PayPal
    updateSubscription('premium');
    alert('Félicitations ! Vous êtes maintenant Premium !');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-lg p-8 mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center">
                <User className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Bonjour, {user.name} !</h1>
                <p className="text-gray-600">{user.email}</p>
                <div className="flex items-center mt-2">
                  {isPremium ? (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      <Crown className="w-4 h-4 mr-1" />
                      Premium
                    </span>
                  ) : (
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                      Gratuit
                    </span>
                  )}
                </div>
              </div>
            </div>
            <button
              onClick={logout}
              className="flex items-center space-x-2 text-gray-600 hover:text-red-600 transition-colors"
            >
              <LogOut className="w-5 h-5" />
              <span>Déconnexion</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Usage Statistics */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Utilisation mensuelle</h2>
              
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    Fichiers traités ce mois
                  </span>
                  <span className="text-sm text-gray-600">
                    {user.usage.filesProcessed} / {user.usage.monthlyLimit}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className={`h-3 rounded-full transition-all duration-300 ${
                      isNearLimit ? 'bg-red-500' : isPremium ? 'bg-yellow-500' : 'bg-blue-500'
                    }`}
                    style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                  ></div>
                </div>
                {isNearLimit && !isPremium && (
                  <p className="text-red-600 text-sm mt-2">
                    ⚠️ Vous approchez de votre limite mensuelle
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <FileText className="w-8 h-8 text-blue-600 mr-3" />
                    <div>
                      <p className="text-2xl font-bold text-blue-900">{user.usage.filesProcessed}</p>
                      <p className="text-blue-700 text-sm">Fichiers traités</p>
                    </div>
                  </div>
                </div>
                <div className="bg-green-50 rounded-lg p-4">
                  <div className="flex items-center">
                    <Calendar className="w-8 h-8 text-green-600 mr-3" />
                    <div>
                      <p className="text-2xl font-bold text-green-900">
                        {Math.max(0, user.usage.monthlyLimit - user.usage.filesProcessed)}
                      </p>
                      <p className="text-green-700 text-sm">Restants</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                <p className="text-sm text-gray-600">
                  <Calendar className="w-4 h-4 inline mr-1" />
                  Prochaine réinitialisation : {user.usage.resetDate.toLocaleDateString('fr-FR')}
                </p>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Activité récente</h2>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <FileText className="w-5 h-5 text-blue-600 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900">PDF fusionné</p>
                      <p className="text-sm text-gray-600">Il y a 2 heures</p>
                    </div>
                  </div>
                  <span className="text-green-600 text-sm">Succès</span>
                </div>
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex items-center">
                    <FileText className="w-5 h-5 text-purple-600 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900">PDF compressé</p>
                      <p className="text-sm text-gray-600">Hier</p>
                    </div>
                  </div>
                  <span className="text-green-600 text-sm">Succès</span>
                </div>
              </div>
            </div>
          </div>

          {/* Subscription Panel */}
          <div className="space-y-6">
            {!isPremium && (
              <div className="bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl shadow-lg p-6 text-white">
                <div className="text-center">
                  <Crown className="w-12 h-12 mx-auto mb-4" />
                  <h3 className="text-xl font-bold mb-2">Passez Premium !</h3>
                  <p className="text-yellow-100 mb-6">
                    Débloquez toutes les fonctionnalités avancées
                  </p>
                  
                  <div className="space-y-3 text-left mb-6">
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                      <span className="text-sm">1000 fichiers par mois</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                      <span className="text-sm">Fichiers jusqu'à 100MB</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                      <span className="text-sm">Traitement prioritaire</span>
                    </div>
                    <div className="flex items-center">
                      <span className="w-2 h-2 bg-white rounded-full mr-3"></span>
                      <span className="text-sm">Support premium</span>
                    </div>
                  </div>

                  <button
                    onClick={handleUpgradeToPremium}
                    className="w-full bg-white text-orange-600 py-3 px-4 rounded-lg font-bold hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2"
                  >
                    <CreditCard className="w-5 h-5" />
                    <span>Passer Premium - 9.99€/mois</span>
                  </button>
                </div>
              </div>
            )}

            {isPremium && (
              <div className="bg-white rounded-2xl shadow-lg p-6">
                <div className="text-center">
                  <Crown className="w-12 h-12 text-yellow-500 mx-auto mb-4" />
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Compte Premium</h3>
                  <p className="text-gray-600 mb-4">
                    Merci d'être un membre Premium !
                  </p>
                  {user.subscriptionExpiry && (
                    <p className="text-sm text-gray-500">
                      Expire le {user.subscriptionExpiry.toLocaleDateString('fr-FR')}
                    </p>
                  )}
                </div>
              </div>
            )}

            {/* Quick Actions */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Actions rapides</h3>
              <div className="space-y-3">
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center">
                    <Settings className="w-5 h-5 text-gray-600 mr-3" />
                    <span className="text-gray-900">Paramètres</span>
                  </div>
                </button>
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center">
                    <FileText className="w-5 h-5 text-gray-600 mr-3" />
                    <span className="text-gray-900">Historique</span>
                  </div>
                </button>
                <button className="w-full flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center">
                    <CreditCard className="w-5 h-5 text-gray-600 mr-3" />
                    <span className="text-gray-900">Facturation</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;
