import React, { useState } from 'react';
import { FileText, Download, ArrowRight } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import apiService from '../services/api';

const MergePDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [downloadUrl, setDownloadUrl] = useState<string>('');
  const [error, setError] = useState<string>('');
  const { t } = useLanguage();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
    setDownloadUrl('');
    setError('');
  };

  const handleMerge = async () => {
    if (files.length < 2) return;

    setIsProcessing(true);
    setError('');

    try {
      const response = await apiService.mergePdfs(files);

      if (response.success && response.data) {
        setDownloadUrl(response.data.downloadUrl || '');
        alert(response.message || 'PDF fusionné avec succès!');
      } else {
        throw new Error(response.error || 'Erreur lors de la fusion');
      }
    } catch (error) {
      console.error('Merge error:', error);
      setError(error instanceof Error ? error.message : 'Erreur lors de la fusion des PDF');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownload = async () => {
    if (!downloadUrl) return;

    try {
      const fileId = downloadUrl.split('/').pop();
      if (!fileId) return;

      const blob = await apiService.downloadFile(fileId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'merged.pdf';
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Download error:', error);
      setError('Erreur lors du téléchargement');
    }
  };

  return (
    <ToolLayout
      title={t('tool.merge.title')}
      description={t('tool.merge.description')}
      icon={<FileText className="w-8 h-8" />}
      color="from-blue-500 to-blue-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={true}
          maxFiles={10}
          title="Sélectionnez vos fichiers PDF"
          description="Glissez-déposez plusieurs fichiers PDF ici ou cliquez pour sélectionner (max 10 fichiers)"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Ordre de fusion ({files.length} fichiers)
            </h3>
            <div className="space-y-2">
              {files.map((file, index) => (
                <div key={index} className="flex items-center justify-between bg-white p-3 rounded-lg shadow-sm">
                  <div className="flex items-center space-x-3">
                    <span className="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-sm font-medium">
                      {index + 1}
                    </span>
                    <FileText className="w-5 h-5 text-slate-500" />
                    <span className="text-sm font-medium text-slate-700">{file.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button className="text-slate-400 hover:text-blue-600 transition-colors">
                      ↑
                    </button>
                    <button className="text-slate-400 hover:text-blue-600 transition-colors">
                      ↓
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <p className="text-red-700">{error}</p>
          </div>
        )}

        {files.length >= 2 && (
          <div className="flex justify-center">
            <button
              onClick={handleMerge}
              disabled={isProcessing}
              className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Fusion en cours...</span>
                </>
              ) : (
                <>
                  <span>{t('button.merge')}</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}

        {downloadUrl && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-semibold text-green-800">PDF fusionné avec succès!</h3>
                <p className="text-green-600">Votre fichier est prêt à être téléchargé.</p>
              </div>
              <button
                onClick={handleDownload}
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center space-x-2"
              >
                <Download className="w-5 h-5" />
                <span>Télécharger</span>
              </button>
            </div>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default MergePDF;